#!/usr/bin/env python3
"""
Gamebook Intelligence Layer
Analyzes NFL gamebook data to create custom team strength models
"""

import os
import re
import json
import pandas as pd
from typing import Dict, <PERSON>, Tuple, Any
from dataclasses import dataclass
from collections import defaultdict
import numpy as np


@dataclass
class GameStats:
    """Individual game statistics."""
    home_team: str
    away_team: str
    home_score: int
    away_score: int
    weather: str
    total_plays: int
    turnovers: Dict[str, int]
    time_of_possession: Dict[str, str]
    third_down_efficiency: Dict[str, str]
    red_zone_efficiency: Dict[str, str]
    rushing_stats: Dict[str, Dict]
    passing_stats: Dict[str, Dict]
    defensive_stats: Dict[str, Dict]


@dataclass
class TeamProfile:
    """Team offensive/defensive profile."""
    team_name: str
    games_played: int
    
    # Offensive metrics
    off_explosive_play_rate: float
    off_success_rate: float
    off_epa_per_play: float
    off_third_down_rate: float
    off_red_zone_rate: float
    off_rush_success: float
    off_pass_success: float
    
    # Defensive metrics
    def_explosive_allowed: float
    def_success_rate_allowed: float
    def_epa_allowed: float
    def_third_down_allowed: float
    def_red_zone_stops: float
    def_rush_defense: float
    def_pass_defense: float
    
    # Contextual factors
    strength_of_schedule: float
    home_away_splits: Dict[str, float]
    weather_performance: Dict[str, float]


class GamebookIntelligence:
    """Analyzes gamebook data to create team intelligence profiles."""
    
    def __init__(self, gamebook_folder: str = "csvs/Gamebook Results"):
        self.gamebook_folder = gamebook_folder
        self.games_data: List[GameStats] = []
        self.team_profiles: Dict[str, TeamProfile] = {}
        self.matchup_matrix: Dict[Tuple[str, str], Dict] = {}
        
        # NFL team name mappings
        self.team_mappings = {
            'Vikings': 'MIN', 'Bears': 'CHI', 'Chiefs': 'KC', 'Chargers': 'LAC',
            'Ravens': 'BAL', 'Bills': 'BUF', '49ers': 'SF', 'Seahawks': 'SEA',
            'Bengals': 'CIN', 'Browns': 'CLE', 'Raiders': 'LV', 'Patriots': 'NE',
            'Bucs': 'TB', 'Falcons': 'ATL', 'Cardinals': 'ARI', 'Saints': 'NO',
            'Cowboys': 'DAL', 'Eagles': 'PHI', 'Dolphins': 'MIA', 'Colts': 'IND',
            'Giants': 'NYG', 'Commanders': 'WAS', 'Lions': 'DET', 'Packers': 'GB',
            'Panthers': 'CAR', 'Jaguars': 'JAX', 'Steelers': 'PIT', 'Jets': 'NYJ',
            'Texans': 'HOU', 'Rams': 'LAR', 'Titans': 'TEN', 'Broncos': 'DEN'
        }
    
    def load_all_gamebooks(self) -> None:
        """Load and parse all gamebook files."""
        print("🔍 LOADING GAMEBOOK DATA...")
        
        gamebook_files = [f for f in os.listdir(self.gamebook_folder) if f.endswith('.md')]
        
        for filename in gamebook_files:
            if filename == 'gamebook.md':  # Skip the original WAS@GB file
                continue
                
            filepath = os.path.join(self.gamebook_folder, filename)
            try:
                game_stats = self._parse_gamebook(filepath)
                if game_stats:
                    self.games_data.append(game_stats)
                    print(f"✅ Loaded: {game_stats.away_team} @ {game_stats.home_team}")
            except Exception as e:
                print(f"❌ Error loading {filename}: {str(e)}")
        
        print(f"📊 Total games loaded: {len(self.games_data)}")
    
    def _parse_gamebook(self, filepath: str) -> GameStats:
        """Parse individual gamebook file."""
        with open(filepath, 'r', encoding='utf-8') as f:
            content = f.read()
        
        # Extract team names from filename or content
        filename = os.path.basename(filepath)
        teams = self._extract_teams_from_filename(filename)
        
        if not teams:
            teams = self._extract_teams_from_content(content)
        
        if not teams:
            return None
        
        away_team, home_team = teams
        
        # Extract game statistics
        stats = GameStats(
            home_team=home_team,
            away_team=away_team,
            home_score=0,
            away_score=0,
            weather=self._extract_weather(content),
            total_plays=0,
            turnovers={home_team: 0, away_team: 0},
            time_of_possession={home_team: "0:00", away_team: "0:00"},
            third_down_efficiency={home_team: "0/0", away_team: "0/0"},
            red_zone_efficiency={home_team: "0/0", away_team: "0/0"},
            rushing_stats={home_team: {}, away_team: {}},
            passing_stats={home_team: {}, away_team: {}},
            defensive_stats={home_team: {}, away_team: {}}
        )
        
        # Parse detailed statistics
        self._parse_score(content, stats)
        self._parse_efficiency_stats(content, stats)
        self._parse_play_data(content, stats)
        
        return stats
    
    def _extract_teams_from_filename(self, filename: str) -> Tuple[str, str]:
        """Extract team names from filename."""
        filename = filename.replace('.md', '').replace('vs', ' ').replace(' at ', ' ')
        
        # Common patterns
        patterns = [
            r'(\w+)\s+(\w+)',  # "Vikings Bears"
            r'(\w+)\s+vs\s+(\w+)',  # "Vikings vs Bears"
            r'(\w+)\s+at\s+(\w+)',  # "Vikings at Bears"
        ]
        
        for pattern in patterns:
            match = re.search(pattern, filename, re.IGNORECASE)
            if match:
                team1, team2 = match.groups()
                # Normalize team names
                team1 = team1.title()
                team2 = team2.title()
                return (team1, team2)  # (away, home)
        
        return None
    
    def _extract_teams_from_content(self, content: str) -> Tuple[str, str]:
        """Extract team names from gamebook content."""
        # Look for team names in the header
        lines = content.split('\n')[:20]  # Check first 20 lines
        
        for line in lines:
            if 'at' in line.lower():
                # Pattern: "Team1 at Team2"
                match = re.search(r'(\w+)\s+at\s+(\w+)', line, re.IGNORECASE)
                if match:
                    away, home = match.groups()
                    return (away.title(), home.title())
        
        return None
    
    def _extract_weather(self, content: str) -> str:
        """Extract weather information."""
        weather_match = re.search(r'Temp:\s*(\d+)°.*?Wind:\s*([^\\n]+)', content)
        if weather_match:
            temp, wind = weather_match.groups()
            return f"{temp}°F, {wind.strip()}"
        return "Unknown"
    
    def _parse_score(self, content: str, stats: GameStats) -> None:
        """Parse final score from gamebook."""
        # Look for score patterns
        score_patterns = [
            r'FINAL\s+SCORE.*?(\d+).*?(\d+)',
            r'PERIOD\s+SCORES.*?(\d+).*?(\d+)',
            r'(\d+)\s*-\s*(\d+)\s*FINAL'
        ]
        
        for pattern in score_patterns:
            match = re.search(pattern, content, re.IGNORECASE | re.DOTALL)
            if match:
                score1, score2 = map(int, match.groups())
                # Determine which is home/away based on context
                stats.away_score = score1
                stats.home_score = score2
                break
    
    def _parse_efficiency_stats(self, content: str, stats: GameStats) -> None:
        """Parse efficiency statistics."""
        # Third down efficiency
        third_down_pattern = r'(\d+)-(\d+)-(\d+\.?\d*)%'
        matches = re.findall(third_down_pattern, content)
        if len(matches) >= 2:
            stats.third_down_efficiency[stats.away_team] = f"{matches[0][0]}/{matches[0][1]}"
            stats.third_down_efficiency[stats.home_team] = f"{matches[1][0]}/{matches[1][1]}"
        
        # Time of possession
        top_pattern = r'(\d+):(\d+)'
        top_matches = re.findall(top_pattern, content)
        if len(top_matches) >= 2:
            stats.time_of_possession[stats.away_team] = f"{top_matches[0][0]}:{top_matches[0][1]}"
            stats.time_of_possession[stats.home_team] = f"{top_matches[1][0]}:{top_matches[1][1]}"
    
    def _parse_play_data(self, content: str, stats: GameStats) -> None:
        """Parse play-by-play data for advanced metrics."""
        # Count total plays
        play_pattern = r'\(\d+:\d+\)'  # Time stamps indicate plays
        stats.total_plays = len(re.findall(play_pattern, content))

        # Initialize comprehensive team stats
        for team in [stats.home_team, stats.away_team]:
            stats.rushing_stats[team] = {
                'attempts': 0, 'yards': 0, 'tds': 0, 'explosive': 0,
                'inside_zone': 0, 'outside_zone': 0, 'power_runs': 0,
                'first_down_rate': 0, 'red_zone_attempts': 0
            }
            stats.passing_stats[team] = {
                'attempts': 0, 'completions': 0, 'yards': 0, 'tds': 0, 'explosive': 0,
                'short_passes': 0, 'intermediate_passes': 0, 'deep_passes': 0,
                'play_action': 0, 'screens': 0, 'third_down_conversions': 0,
                'red_zone_attempts': 0, 'pressure_rate': 0
            }
            stats.defensive_stats[team] = {
                'sacks': 0, 'ints': 0, 'fumbles': 0, 'stops': 0,
                'explosive_allowed': 0, 'third_down_stops': 0,
                'red_zone_stops': 0, 'pressure_generated': 0,
                'coverage_busts': 0, 'run_stuffs': 0
            }

        # Parse detailed play-by-play
        self._extract_play_types(content, stats)
        self._extract_situational_stats(content, stats)
        self._extract_defensive_metrics(content, stats)

    def _extract_play_types(self, content: str, stats: GameStats) -> None:
        """Extract play type analysis from gamebook."""
        lines = content.split('\n')

        for line in lines:
            # Look for play descriptions
            if '(' in line and ')' in line and any(word in line.lower() for word in ['rush', 'pass', 'run', 'throw']):
                # Determine team and play type
                team = self._identify_team_from_play(line, stats)
                if not team:
                    continue

                # Rushing plays
                if any(word in line.lower() for word in ['rush', 'run', 'left tackle', 'right tackle', 'up the middle']):
                    stats.rushing_stats[team]['attempts'] += 1

                    # Extract yards
                    yards_match = re.search(r'for (\d+) yard', line)
                    if yards_match:
                        yards = int(yards_match.group(1))
                        stats.rushing_stats[team]['yards'] += yards
                        if yards >= 15:
                            stats.rushing_stats[team]['explosive'] += 1

                    # Identify run type
                    if 'left tackle' in line.lower() or 'right tackle' in line.lower():
                        stats.rushing_stats[team]['outside_zone'] += 1
                    elif 'up the middle' in line.lower():
                        stats.rushing_stats[team]['inside_zone'] += 1

                # Passing plays
                elif any(word in line.lower() for word in ['pass', 'throw', 'complete', 'incomplete']):
                    stats.passing_stats[team]['attempts'] += 1

                    if 'complete' in line.lower():
                        stats.passing_stats[team]['completions'] += 1

                        # Extract yards
                        yards_match = re.search(r'for (\d+) yard', line)
                        if yards_match:
                            yards = int(yards_match.group(1))
                            stats.passing_stats[team]['yards'] += yards
                            if yards >= 20:
                                stats.passing_stats[team]['explosive'] += 1

                            # Classify pass distance
                            if yards <= 5:
                                stats.passing_stats[team]['short_passes'] += 1
                            elif yards <= 15:
                                stats.passing_stats[team]['intermediate_passes'] += 1
                            else:
                                stats.passing_stats[team]['deep_passes'] += 1

                    # Screen passes
                    if 'screen' in line.lower():
                        stats.passing_stats[team]['screens'] += 1

    def _extract_situational_stats(self, content: str, stats: GameStats) -> None:
        """Extract situational statistics."""
        lines = content.split('\n')

        for line in lines:
            # Third down conversions
            if '3rd' in line and 'FIRST DOWN' in line.upper():
                team = self._identify_team_from_play(line, stats)
                if team:
                    stats.passing_stats[team]['third_down_conversions'] += 1

            # Red zone plays (simplified detection)
            if any(word in line.lower() for word in ['goal line', 'red zone', 'touchdown']):
                team = self._identify_team_from_play(line, stats)
                if team:
                    if 'rush' in line.lower() or 'run' in line.lower():
                        stats.rushing_stats[team]['red_zone_attempts'] += 1
                    elif 'pass' in line.lower():
                        stats.passing_stats[team]['red_zone_attempts'] += 1

    def _extract_defensive_metrics(self, content: str, stats: GameStats) -> None:
        """Extract defensive performance metrics."""
        lines = content.split('\n')

        for line in lines:
            # Sacks
            if 'sack' in line.lower():
                # Sack is credited to the defense, so find the team being sacked
                for team in [stats.home_team, stats.away_team]:
                    opponent = stats.away_team if team == stats.home_team else stats.home_team
                    if team.lower() in line.lower():
                        stats.defensive_stats[opponent]['sacks'] += 1
                        stats.defensive_stats[opponent]['pressure_generated'] += 1

            # Interceptions
            if 'intercept' in line.lower():
                team = self._identify_team_from_play(line, stats)
                if team:
                    opponent = stats.away_team if team == stats.home_team else stats.home_team
                    stats.defensive_stats[team]['ints'] += 1

            # Fumbles
            if 'fumble' in line.lower() and 'recover' in line.lower():
                team = self._identify_team_from_play(line, stats)
                if team:
                    stats.defensive_stats[team]['fumbles'] += 1

    def _identify_team_from_play(self, line: str, stats: GameStats) -> str:
        """Identify which team is executing the play."""
        line_lower = line.lower()

        # Look for team names or abbreviations
        if stats.home_team.lower() in line_lower:
            return stats.home_team
        elif stats.away_team.lower() in line_lower:
            return stats.away_team

        # Look for common abbreviations
        home_abbrev = self.team_mappings.get(stats.home_team, stats.home_team[:3]).lower()
        away_abbrev = self.team_mappings.get(stats.away_team, stats.away_team[:3]).lower()

        if home_abbrev in line_lower:
            return stats.home_team
        elif away_abbrev in line_lower:
            return stats.away_team

        return None
    
    def build_team_profiles(self) -> None:
        """Build comprehensive team profiles from game data."""
        print("🏗️ BUILDING TEAM PROFILES...")

        team_stats = defaultdict(lambda: {
            'games': 0,
            'total_plays': 0,
            'explosive_plays': 0,
            'successful_plays': 0,
            'points_scored': 0,
            'points_allowed': 0,
            'third_down_conversions': 0,
            'third_down_attempts': 0,
            'red_zone_scores': 0,
            'red_zone_attempts': 0,
            'opponents': [],
            # Enhanced offensive metrics
            'rush_attempts': 0,
            'rush_yards': 0,
            'rush_explosive': 0,
            'pass_attempts': 0,
            'pass_completions': 0,
            'pass_yards': 0,
            'pass_explosive': 0,
            'short_passes': 0,
            'deep_passes': 0,
            'play_action_success': 0,
            'screen_success': 0,
            # Enhanced defensive metrics
            'sacks_allowed': 0,
            'ints_thrown': 0,
            'fumbles_lost': 0,
            'explosive_allowed': 0,
            'third_down_stops': 0,
            'red_zone_stops': 0,
            'pressure_allowed': 0,
            # Situational metrics
            'first_quarter_points': 0,
            'fourth_quarter_points': 0,
            'home_performance': {'games': 0, 'points': 0},
            'away_performance': {'games': 0, 'points': 0},
            'weather_games': 0
        })
        
        # Aggregate stats from all games
        for game in self.games_data:
            for team in [game.home_team, game.away_team]:
                team_stats[team]['games'] += 1
                team_stats[team]['total_plays'] += game.total_plays // 2  # Rough estimate

                # Add opponent for strength of schedule
                opponent = game.away_team if team == game.home_team else game.home_team
                team_stats[team]['opponents'].append(opponent)

                # Points scored/allowed
                if team == game.home_team:
                    team_stats[team]['points_scored'] += game.home_score
                    team_stats[team]['points_allowed'] += game.away_score
                    team_stats[team]['home_performance']['games'] += 1
                    team_stats[team]['home_performance']['points'] += game.home_score
                else:
                    team_stats[team]['points_scored'] += game.away_score
                    team_stats[team]['points_allowed'] += game.home_score
                    team_stats[team]['away_performance']['games'] += 1
                    team_stats[team]['away_performance']['points'] += game.away_score

                # Aggregate detailed stats
                if team in game.rushing_stats:
                    rush_stats = game.rushing_stats[team]
                    team_stats[team]['rush_attempts'] += rush_stats.get('attempts', 0)
                    team_stats[team]['rush_yards'] += rush_stats.get('yards', 0)
                    team_stats[team]['rush_explosive'] += rush_stats.get('explosive', 0)

                if team in game.passing_stats:
                    pass_stats = game.passing_stats[team]
                    team_stats[team]['pass_attempts'] += pass_stats.get('attempts', 0)
                    team_stats[team]['pass_completions'] += pass_stats.get('completions', 0)
                    team_stats[team]['pass_yards'] += pass_stats.get('yards', 0)
                    team_stats[team]['pass_explosive'] += pass_stats.get('explosive', 0)
                    team_stats[team]['short_passes'] += pass_stats.get('short_passes', 0)
                    team_stats[team]['deep_passes'] += pass_stats.get('deep_passes', 0)

                # Defensive stats (what opponent did against this team)
                if opponent in game.rushing_stats:
                    opp_rush = game.rushing_stats[opponent]
                    team_stats[team]['explosive_allowed'] += opp_rush.get('explosive', 0)

                if opponent in game.passing_stats:
                    opp_pass = game.passing_stats[opponent]
                    team_stats[team]['explosive_allowed'] += opp_pass.get('explosive', 0)

                # Weather tracking
                if 'wind' in game.weather.lower() or 'rain' in game.weather.lower():
                    team_stats[team]['weather_games'] += 1
        
        # Create enhanced team profiles
        for team, stats in team_stats.items():
            if stats['games'] == 0:
                continue

            # Calculate sophisticated ratings
            profile = TeamProfile(
                team_name=team,
                games_played=stats['games'],
                # Enhanced offensive metrics
                off_explosive_play_rate=self._calculate_enhanced_explosive_rate(stats),
                off_success_rate=self._calculate_enhanced_success_rate(stats),
                off_epa_per_play=self._calculate_enhanced_epa(stats),
                off_third_down_rate=self._calculate_third_down_rate(stats),
                off_red_zone_rate=self._calculate_red_zone_rate(stats),
                off_rush_success=self._calculate_rush_success(stats),
                off_pass_success=self._calculate_pass_success(stats),
                # Enhanced defensive metrics
                def_explosive_allowed=self._calculate_explosive_allowed(stats),
                def_success_rate_allowed=self._calculate_def_success_allowed(stats),
                def_epa_allowed=self._calculate_def_epa_allowed(stats),
                def_third_down_allowed=self._calculate_def_third_down_allowed(stats),
                def_red_zone_stops=self._calculate_red_zone_stops(stats),
                def_rush_defense=self._calculate_rush_defense(stats),
                def_pass_defense=self._calculate_pass_defense(stats),
                # Contextual factors
                strength_of_schedule=self._calculate_sos(stats['opponents']),
                home_away_splits=self._calculate_home_away_splits(stats),
                weather_performance=self._calculate_weather_performance(stats)
            )

            self.team_profiles[team] = profile
        
        print(f"✅ Built profiles for {len(self.team_profiles)} teams")
    
    def _calculate_explosive_rate(self, stats: Dict) -> float:
        """Calculate explosive play rate (15+ yard plays)."""
        if stats['total_plays'] == 0:
            return 0.0
        return stats['explosive_plays'] / stats['total_plays']
    
    def _calculate_success_rate(self, stats: Dict) -> float:
        """Calculate overall success rate."""
        if stats['total_plays'] == 0:
            return 0.0
        return stats['successful_plays'] / stats['total_plays']
    
    def _calculate_epa(self, stats: Dict) -> float:
        """Calculate EPA per play (simplified)."""
        if stats['total_plays'] == 0:
            return 0.0
        # Simplified EPA based on points per play
        return (stats['points_scored'] - stats['points_allowed']) / stats['total_plays']
    
    def _calculate_third_down_rate(self, stats: Dict) -> float:
        """Calculate third down conversion rate."""
        if stats['third_down_attempts'] == 0:
            return 0.0
        return stats['third_down_conversions'] / stats['third_down_attempts']
    
    def _calculate_red_zone_rate(self, stats: Dict) -> float:
        """Calculate red zone scoring rate."""
        if stats['red_zone_attempts'] == 0:
            return 0.0
        return stats['red_zone_scores'] / stats['red_zone_attempts']
    
    def _calculate_enhanced_explosive_rate(self, stats: Dict) -> float:
        """Calculate enhanced explosive play rate."""
        total_explosive = stats['rush_explosive'] + stats['pass_explosive']
        total_plays = stats['rush_attempts'] + stats['pass_attempts']
        return total_explosive / total_plays if total_plays > 0 else 0.0

    def _calculate_enhanced_success_rate(self, stats: Dict) -> float:
        """Calculate enhanced success rate based on multiple factors."""
        if stats['total_plays'] == 0:
            return 0.0

        # Success rate based on points per play and explosive plays
        points_per_play = stats['points_scored'] / stats['total_plays']
        explosive_rate = self._calculate_enhanced_explosive_rate(stats)

        # Normalize and combine
        success_rate = (points_per_play * 5) + (explosive_rate * 2)  # Weight points higher
        return min(1.0, max(0.0, success_rate))

    def _calculate_enhanced_epa(self, stats: Dict) -> float:
        """Calculate enhanced EPA per play."""
        if stats['total_plays'] == 0:
            return 0.0

        # EPA approximation based on points differential and efficiency
        point_differential = stats['points_scored'] - stats['points_allowed']
        explosive_bonus = (stats['rush_explosive'] + stats['pass_explosive']) * 0.5

        return (point_differential + explosive_bonus) / stats['total_plays']

    def _calculate_rush_success(self, stats: Dict) -> float:
        """Calculate rushing success rate."""
        if stats['rush_attempts'] == 0:
            return 0.5

        # Success based on yards per attempt and explosive plays
        ypa = stats['rush_yards'] / stats['rush_attempts']
        explosive_rate = stats['rush_explosive'] / stats['rush_attempts']

        # NFL average is ~4.3 YPA
        success_rate = (ypa / 4.3) * 0.7 + explosive_rate * 0.3
        return min(1.0, max(0.0, success_rate))

    def _calculate_pass_success(self, stats: Dict) -> float:
        """Calculate passing success rate."""
        if stats['pass_attempts'] == 0:
            return 0.5

        completion_rate = stats['pass_completions'] / stats['pass_attempts']
        ypa = stats['pass_yards'] / stats['pass_attempts'] if stats['pass_attempts'] > 0 else 0
        explosive_rate = stats['pass_explosive'] / stats['pass_attempts']

        # NFL average completion ~65%, YPA ~7.0
        success_rate = (completion_rate * 0.4) + (ypa / 7.0 * 0.4) + (explosive_rate * 0.2)
        return min(1.0, max(0.0, success_rate))

    def _calculate_explosive_allowed(self, stats: Dict) -> float:
        """Calculate explosive plays allowed rate."""
        total_plays_faced = stats['total_plays']  # Opponent plays
        return stats['explosive_allowed'] / total_plays_faced if total_plays_faced > 0 else 0.1

    def _calculate_def_success_allowed(self, stats: Dict) -> float:
        """Calculate defensive success rate allowed."""
        # Lower is better for defense
        points_allowed_per_play = stats['points_allowed'] / stats['total_plays'] if stats['total_plays'] > 0 else 0
        explosive_allowed_rate = self._calculate_explosive_allowed(stats)

        # Combine factors (higher values = worse defense)
        success_allowed = (points_allowed_per_play * 5) + (explosive_allowed_rate * 2)
        return min(1.0, max(0.0, success_allowed))

    def _calculate_def_epa_allowed(self, stats: Dict) -> float:
        """Calculate defensive EPA allowed."""
        if stats['total_plays'] == 0:
            return 0.0

        # Negative EPA is good for defense
        point_differential = stats['points_allowed'] - stats['points_scored']
        explosive_penalty = stats['explosive_allowed'] * 0.5

        return (point_differential + explosive_penalty) / stats['total_plays']

    def _calculate_def_third_down_allowed(self, stats: Dict) -> float:
        """Calculate third down conversion rate allowed."""
        # Placeholder - would need more detailed parsing
        return 0.4  # NFL average ~40%

    def _calculate_red_zone_stops(self, stats: Dict) -> float:
        """Calculate red zone stop rate."""
        # Placeholder - would need more detailed parsing
        return 0.6  # Assume 60% stop rate

    def _calculate_rush_defense(self, stats: Dict) -> float:
        """Calculate rush defense rating."""
        # Placeholder - would need opponent rushing stats
        return 0.5

    def _calculate_pass_defense(self, stats: Dict) -> float:
        """Calculate pass defense rating."""
        # Placeholder - would need opponent passing stats
        return 0.5

    def _calculate_home_away_splits(self, stats: Dict) -> Dict[str, float]:
        """Calculate home/away performance splits."""
        home_games = stats['home_performance']['games']
        away_games = stats['away_performance']['games']

        if home_games == 0 and away_games == 0:
            return {'home': 0.5, 'away': 0.5}

        home_ppg = stats['home_performance']['points'] / home_games if home_games > 0 else 0
        away_ppg = stats['away_performance']['points'] / away_games if away_games > 0 else 0

        # Normalize to 0-1 scale
        total_ppg = home_ppg + away_ppg
        if total_ppg == 0:
            return {'home': 0.5, 'away': 0.5}

        return {
            'home': home_ppg / total_ppg,
            'away': away_ppg / total_ppg
        }

    def _calculate_weather_performance(self, stats: Dict) -> Dict[str, float]:
        """Calculate weather performance."""
        weather_games = stats['weather_games']
        total_games = stats['games']

        if total_games == 0:
            return {'good': 0.5, 'bad': 0.5}

        good_weather_games = total_games - weather_games

        return {
            'good': good_weather_games / total_games,
            'bad': weather_games / total_games
        }

    def _calculate_sos(self, opponents: List[str]) -> float:
        """Calculate strength of schedule."""
        if not opponents:
            return 0.5

        # Simplified SOS - would need more sophisticated calculation
        return 0.5  # Placeholder
    
    def create_matchup_matrix(self) -> None:
        """Create matchup advantage matrix."""
        print("⚔️ CREATING MATCHUP MATRIX...")
        
        for away_team, away_profile in self.team_profiles.items():
            for home_team, home_profile in self.team_profiles.items():
                if away_team == home_team:
                    continue
                
                matchup_key = (away_team, home_team)
                
                # Calculate matchup advantages
                advantages = {
                    'offensive_advantage': self._calculate_offensive_advantage(away_profile, home_profile),
                    'defensive_advantage': self._calculate_defensive_advantage(away_profile, home_profile),
                    'pace_advantage': self._calculate_pace_advantage(away_profile, home_profile),
                    'situational_advantages': self._calculate_situational_advantages(away_profile, home_profile),
                    'overall_edge': 0.0
                }
                
                # Calculate overall edge
                advantages['overall_edge'] = (
                    advantages['offensive_advantage'] * 0.4 +
                    advantages['defensive_advantage'] * 0.4 +
                    advantages['pace_advantage'] * 0.1 +
                    sum(advantages['situational_advantages'].values()) * 0.1
                )
                
                self.matchup_matrix[matchup_key] = advantages
        
        print(f"✅ Created matchup matrix for {len(self.matchup_matrix)} matchups")
    
    def _calculate_offensive_advantage(self, team1: TeamProfile, team2: TeamProfile) -> float:
        """Calculate offensive advantage of team1 vs team2's defense."""
        # Compare team1's offense vs team2's defense
        off_vs_def = (
            team1.off_success_rate - team2.def_success_rate_allowed +
            team1.off_explosive_play_rate - team2.def_explosive_allowed +
            team1.off_epa_per_play - team2.def_epa_allowed
        ) / 3
        
        return max(-1.0, min(1.0, off_vs_def))  # Normalize to [-1, 1]
    
    def _calculate_defensive_advantage(self, team1: TeamProfile, team2: TeamProfile) -> float:
        """Calculate defensive advantage of team1 vs team2's offense."""
        # Compare team1's defense vs team2's offense
        def_vs_off = (
            team1.def_success_rate_allowed - team2.off_success_rate +
            team1.def_explosive_allowed - team2.off_explosive_play_rate +
            team1.def_epa_allowed - team2.off_epa_per_play
        ) / 3
        
        return max(-1.0, min(1.0, -def_vs_off))  # Negative because lower is better for defense
    
    def _calculate_pace_advantage(self, team1: TeamProfile, team2: TeamProfile) -> float:
        """Calculate pace/tempo advantage."""
        # Placeholder - would need play count data
        return 0.0
    
    def _calculate_situational_advantages(self, team1: TeamProfile, team2: TeamProfile) -> Dict[str, float]:
        """Calculate situational advantages."""
        return {
            'third_down': team1.off_third_down_rate - team2.def_third_down_allowed,
            'red_zone': team1.off_red_zone_rate - team2.def_red_zone_stops,
            'explosive_plays': team1.off_explosive_play_rate - team2.def_explosive_allowed
        }
    
    def get_team_rankings(self) -> Dict[str, List[Tuple[str, float]]]:
        """Get team rankings by various metrics."""
        rankings = {
            'offensive_efficiency': [],
            'defensive_efficiency': [],
            'overall_rating': []
        }
        
        for team, profile in self.team_profiles.items():
            # Offensive efficiency
            off_score = (profile.off_success_rate + profile.off_explosive_play_rate + 
                        profile.off_epa_per_play + profile.off_third_down_rate + 
                        profile.off_red_zone_rate) / 5
            rankings['offensive_efficiency'].append((team, off_score))
            
            # Defensive efficiency  
            def_score = (profile.def_success_rate_allowed + profile.def_explosive_allowed + 
                        abs(profile.def_epa_allowed) + profile.def_third_down_allowed) / 4
            rankings['defensive_efficiency'].append((team, 1 - def_score))  # Lower is better
            
            # Overall rating
            overall = (off_score + (1 - def_score)) / 2
            rankings['overall_rating'].append((team, overall))
        
        # Sort rankings
        for category in rankings:
            rankings[category].sort(key=lambda x: x[1], reverse=True)
        
        return rankings
    
    def get_matchup_analysis(self, away_team: str, home_team: str) -> Dict[str, Any]:
        """Get detailed matchup analysis for specific game."""
        matchup_key = (away_team, home_team)
        
        if matchup_key not in self.matchup_matrix:
            return {'error': f'Matchup {away_team} @ {home_team} not found'}
        
        away_profile = self.team_profiles.get(away_team)
        home_profile = self.team_profiles.get(home_team)
        matchup_data = self.matchup_matrix[matchup_key]
        
        analysis = {
            'matchup': f"{away_team} @ {home_team}",
            'overall_edge': matchup_data['overall_edge'],
            'key_advantages': self._identify_key_advantages(matchup_data),
            'team_profiles': {
                away_team: self._profile_summary(away_profile),
                home_team: self._profile_summary(home_profile)
            },
            'projection_adjustments': self._calculate_projection_adjustments(matchup_data),
            'confidence_factors': self._calculate_confidence_factors(away_profile, home_profile)
        }
        
        return analysis
    
    def _identify_key_advantages(self, matchup_data: Dict) -> List[str]:
        """Identify key matchup advantages."""
        advantages = []
        
        if matchup_data['offensive_advantage'] > 0.2:
            advantages.append("Strong offensive advantage")
        elif matchup_data['offensive_advantage'] < -0.2:
            advantages.append("Offensive disadvantage")
        
        if matchup_data['defensive_advantage'] > 0.2:
            advantages.append("Strong defensive advantage")
        elif matchup_data['defensive_advantage'] < -0.2:
            advantages.append("Defensive disadvantage")
        
        for situation, value in matchup_data['situational_advantages'].items():
            if abs(value) > 0.15:
                direction = "advantage" if value > 0 else "disadvantage"
                advantages.append(f"{situation.replace('_', ' ').title()} {direction}")
        
        return advantages
    
    def _profile_summary(self, profile: TeamProfile) -> Dict[str, Any]:
        """Create team profile summary."""
        if not profile:
            return {}
        
        return {
            'games_played': profile.games_played,
            'offensive_rating': (profile.off_success_rate + profile.off_explosive_play_rate + 
                               profile.off_epa_per_play) / 3,
            'defensive_rating': 1 - (profile.def_success_rate_allowed + profile.def_explosive_allowed + 
                                   abs(profile.def_epa_allowed)) / 3,
            'key_strengths': self._identify_strengths(profile),
            'key_weaknesses': self._identify_weaknesses(profile)
        }
    
    def _identify_strengths(self, profile: TeamProfile) -> List[str]:
        """Identify team strengths."""
        strengths = []
        
        if profile.off_explosive_play_rate > 0.15:
            strengths.append("Explosive offense")
        if profile.off_third_down_rate > 0.45:
            strengths.append("Third down efficiency")
        if profile.def_red_zone_stops > 0.65:
            strengths.append("Red zone defense")
        
        return strengths
    
    def _identify_weaknesses(self, profile: TeamProfile) -> List[str]:
        """Identify team weaknesses."""
        weaknesses = []
        
        if profile.off_success_rate < 0.4:
            weaknesses.append("Inconsistent offense")
        if profile.def_explosive_allowed > 0.12:
            weaknesses.append("Vulnerable to big plays")
        if profile.def_third_down_allowed > 0.45:
            weaknesses.append("Third down defense")
        
        return weaknesses
    
    def _calculate_projection_adjustments(self, matchup_data: Dict) -> Dict[str, float]:
        """Calculate projection adjustments based on matchup."""
        return {
            'offensive_boost': max(0, matchup_data['offensive_advantage'] * 0.15),
            'defensive_penalty': max(0, -matchup_data['defensive_advantage'] * 0.10),
            'situational_boost': sum(max(0, v * 0.05) for v in matchup_data['situational_advantages'].values()),
            'overall_adjustment': matchup_data['overall_edge'] * 0.12
        }
    
    def _calculate_confidence_factors(self, away_profile: TeamProfile, home_profile: TeamProfile) -> Dict[str, float]:
        """Calculate confidence factors for projections."""
        factors = {
            'sample_size': min(away_profile.games_played, home_profile.games_played) / 10,
            'consistency': 0.8,  # Placeholder
            'matchup_clarity': 0.7,  # Placeholder
            'overall_confidence': 0.75
        }
        
        factors['overall_confidence'] = (factors['sample_size'] + factors['consistency'] + 
                                       factors['matchup_clarity']) / 3
        
        return factors
    
    def run_full_analysis(self) -> None:
        """Run complete gamebook intelligence analysis."""
        print("🚀 RUNNING FULL GAMEBOOK INTELLIGENCE ANALYSIS")
        print("=" * 50)
        
        self.load_all_gamebooks()
        self.build_team_profiles()
        self.create_matchup_matrix()
        
        print("\n✅ GAMEBOOK INTELLIGENCE ANALYSIS COMPLETE!")
        print(f"📊 Teams analyzed: {len(self.team_profiles)}")
        print(f"⚔️ Matchups created: {len(self.matchup_matrix)}")


def main():
    """Example usage of Gamebook Intelligence."""
    intelligence = GamebookIntelligence()
    intelligence.run_full_analysis()
    
    # Show team rankings
    rankings = intelligence.get_team_rankings()
    
    print("\n🏆 TOP 5 OFFENSIVE TEAMS:")
    for i, (team, score) in enumerate(rankings['offensive_efficiency'][:5], 1):
        print(f"   {i}. {team}: {score:.3f}")
    
    print("\n🛡️ TOP 5 DEFENSIVE TEAMS:")
    for i, (team, score) in enumerate(rankings['defensive_efficiency'][:5], 1):
        print(f"   {i}. {team}: {score:.3f}")
    
    # Example matchup analysis
    if len(intelligence.team_profiles) >= 2:
        teams = list(intelligence.team_profiles.keys())
        example_matchup = intelligence.get_matchup_analysis(teams[0], teams[1])
        
        print(f"\n⚔️ EXAMPLE MATCHUP ANALYSIS:")
        print(f"   {example_matchup['matchup']}")
        print(f"   Overall Edge: {example_matchup['overall_edge']:.3f}")
        print(f"   Key Advantages: {', '.join(example_matchup['key_advantages'])}")


if __name__ == "__main__":
    main()
